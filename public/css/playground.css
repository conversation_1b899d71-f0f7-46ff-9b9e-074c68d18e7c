/**
 * Playground-specific CSS
 * This file contains styles that are only needed for the playground section
 * It's loaded dynamically when the user navigates to the playground
 */

/* Additional playground animations */
@keyframes float-playground {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse-playground {
  0%, 100% {
    opacity: 0.9;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced playground-specific styles */
.playground-enhanced-element {
  animation: float-playground 8s ease-in-out infinite;
  will-change: transform;
  transform: translateZ(0);
}

.playground-pulse-element {
  animation: pulse-playground 8s ease-in-out infinite;
  will-change: opacity;
}

/* Additional playground card styles */
.playground-card {
  transition: all 0.3s ease;
  backface-visibility: hidden;
}

.playground-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced code editor styles */
.playground-code-editor {
  font-family: 'Source Code Pro', monospace;
  line-height: 1.5;
  tab-size: 2;
}

.playground-code-editor .cm-content {
  padding: 10px 0;
}

.playground-code-editor .cm-line {
  padding: 0 10px;
}

/* Enhanced testing environment styles */
.playground-testing-environment {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.playground-testing-environment iframe {
  border: none;
  width: 100%;
  height: 100%;
}

/* Enhanced panel styles */
.playground-panel {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.8);
}

/* Enhanced button styles */
.playground-button {
  position: relative;
  overflow: hidden;
}

.playground-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.playground-button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Enhanced scrollbar styles */
.playground-custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.playground-custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.playground-custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.playground-custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Print styles for playground */
@media print {
  .playground-no-print {
    display: none !important;
  }
  
  .playground-print-only {
    display: block !important;
  }
  
  .playground-container {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Mobile optimizations for Help page */
@media (max-width: 768px) {
  /* Help page mobile improvements */
  .help-mobile-tabs {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 4px !important;
    height: auto !important;
  }
  
  .help-mobile-tab {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 12px 8px !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
    height: auto !important;
    min-height: 60px !important;
  }
  
  .help-mobile-tab-icon {
    margin-bottom: 4px !important;
    margin-right: 0 !important;
  }
  
  /* Mobile search improvements */
  .help-mobile-search {
    padding-left: 40px !important;
    padding-right: 60px !important;
    height: 48px !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
  
  /* Mobile content improvements */
  .help-mobile-content {
    padding: 16px !important;
  }
  
  .help-mobile-item {
    padding: 16px !important;
    margin-bottom: 16px !important;
  }
  
  .help-mobile-title {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
  }
  
  .help-mobile-text {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    padding-left: 32px !important;
  }
  
  /* Mobile accordion improvements */
  .help-mobile-accordion-trigger {
    padding: 12px 8px !important;
    font-size: 1rem !important;
  }
  
  .help-mobile-accordion-content {
    padding: 8px 8px 16px 8px !important;
  }
  
  /* Mobile badge improvements */
  .help-mobile-badge {
    font-size: 0.75rem !important;
    padding: 4px 8px !important;
  }
  
  /* Mobile hero section */
  .help-mobile-hero {
    padding: 16px !important;
    margin-bottom: 24px !important;
  }
  
  .help-mobile-hero-title {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 12px !important;
  }
  
  .help-mobile-hero-description {
    font-size: 1rem !important;
    line-height: 1.4 !important;
    margin-bottom: 16px !important;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .help-touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
  }
  
  .help-tap-highlight {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .help-landscape-scroll {
    height: calc(100vh - 200px) !important;
    min-height: 300px !important;
  }
  
  .help-landscape-hero {
    padding: 12px 16px !important;
    margin-bottom: 16px !important;
  }
}
