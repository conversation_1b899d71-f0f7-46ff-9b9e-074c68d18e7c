
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#008080" />
    <title>Nam Le | Developer & QA Engineer</title>
    <meta name="description" content="Portfolio of Nam Le - Developer & QA Engineer specializing in building robust software solutions through meticulous development and comprehensive testing." />
    <meta name="author" content="Nam Le" />
    <meta name="keywords" content="developer, QA engineer, software testing, web development, portfolio" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon.ico" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Critical resources are now managed by ResourceManager -->

    <!-- Inline critical CSS to prevent render blocking -->
    <style>
      /* Minimal critical CSS for initial render */
      :root {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
      }
      #root {
        min-height: 100vh;
      }

    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
