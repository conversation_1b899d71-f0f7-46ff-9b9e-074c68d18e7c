/**
 * Optimized LocalStorageService using idb-keyval for better performance
 * Reduced from 6.2KB to ~1KB using IndexedDB with localStorage fallback
 */
import { get, set, del, keys } from 'idb-keyval';
import { User } from '../types/playground';
import { nanoid } from 'nanoid';

// Error logging utility to prevent sensitive information exposure
const logError = (message: string, error?: unknown): void => {
  if (process.env.NODE_ENV === 'development') {
    console.error(`[LocalStorageService] ${message}`, error);
  }
};

const logWarning = (message: string, error?: unknown): void => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[LocalStorageService] ${message}`, error);
  }
};

// Input validation helpers
const validateKey = (key: string): void => {
  if (!key || typeof key !== 'string' || key.trim().length === 0) {
    throw new Error('Storage key is required and must be a non-empty string');
  }
};

const validateUser = (user: User): void => {
  if (!user || typeof user !== 'object') {
    throw new Error('User data is required and must be an object');
  }
  if (!user.id || typeof user.id !== 'string') {
    throw new Error('User must have a valid ID');
  }
  if (!user.username || typeof user.username !== 'string') {
    throw new Error('User must have a valid username');
  }
};

const validatePoints = (points: number): void => {
  if (typeof points !== 'number' || points < 0 || !Number.isInteger(points)) {
    throw new Error('Points must be a non-negative integer');
  }
};

const validateBadge = (badge: string): void => {
  if (!badge || typeof badge !== 'string' || badge.trim().length === 0) {
    throw new Error('Badge is required and must be a non-empty string');
  }
};

// Storage keys
const STORAGE_KEYS = {
  USER: 'testing_playground_user',
  USER_PROGRESS: 'testing_playground_user_progress',
  USER_CHALLENGES: 'testing_playground_user_challenges',
  USER_ACHIEVEMENTS: 'testing_playground_user_achievements',
  USER_ACTIVITIES: 'testing_playground_user_activities'
} as const;

// Helper to generate a unique ID using nanoid
const generateId = (): string => {
  return nanoid();
};

// Default guest user factory
const createGuestUser = (): User => ({
  id: generateId(),
  username: 'guest',
  email: '<EMAIL>',
  displayName: 'Guest User',
  role: 'user',
  createdAt: new Date().toISOString(),
  lastLoginAt: new Date().toISOString(),
  level: 1,
  points: 0,
  badges: ['newcomer']
});

// User data validation schema
const isValidUser = (data: unknown): data is User => {
  if (!data || typeof data !== 'object') return false;
  
  const user = data as Record<string, unknown>;
  return (
    typeof user.id === 'string' &&
    typeof user.username === 'string' &&
    typeof user.email === 'string' &&
    typeof user.displayName === 'string' &&
    typeof user.role === 'string' &&
    typeof user.points === 'number' &&
    typeof user.level === 'number' &&
    Array.isArray(user.badges) &&
    user.badges.every((badge: unknown) => typeof badge === 'string') &&
    user.points >= 0 && user.points <= 1000000 &&
    user.level >= 1 && user.level <= 100
  );
};

/**
 * Optimized LocalStorageService using idb-keyval for better performance
 */
export class LocalStorageService {
  private static userCache: User | null = null;
  private static cacheTimestamp = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private static cacheCleanupInterval: NodeJS.Timeout | null = null;

  /**
   * Get storage value with automatic fallback to localStorage
   */
  private static async getStorageValue<T>(key: string): Promise<T | null> {
    validateKey(key);

    try {
      return await get(key) || null;
    } catch (error) {
      // Fallback to localStorage if IndexedDB fails
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      } catch (fallbackError) {
        logError('Both IndexedDB and localStorage failed', { error, fallbackError });
        return null;
      }
    }
  }

  /**
   * Set storage value with automatic fallback to localStorage
   */
  private static async setStorageValue<T>(key: string, value: T): Promise<void> {
    validateKey(key);
    if (value === undefined) {
      throw new Error('Value cannot be undefined');
    }

    try {
      await set(key, value);
    } catch (error) {
      // Fallback to localStorage if IndexedDB fails
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (fallbackError) {
        logError('Both IndexedDB and localStorage failed', { error, fallbackError });
        throw new Error('Failed to save data');
      }
    }
  }

  /**
   * Delete storage value with automatic fallback
   */
  private static async deleteStorageValue(key: string): Promise<void> {
    validateKey(key);

    try {
      await del(key);
    } catch (error) {
      try {
        localStorage.removeItem(key);
      } catch (fallbackError) {
        logError('Both IndexedDB and localStorage failed', { error, fallbackError });
      }
    }
  }

  /**
   * Get current user with caching and validation
   */
  static async getCurrentUser(): Promise<User> {
    const now = Date.now();
    
    // Return cached user if still valid
    if (this.userCache && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      return this.userCache;
    }

    try {
      const userData = await this.getStorageValue<User>(STORAGE_KEYS.USER);
      
      if (userData && isValidUser(userData)) {
        this.userCache = userData;
        this.cacheTimestamp = now;
        return userData;
      }
      
      // Create new guest user if validation fails
      logWarning('Invalid user data detected, creating new guest user');
      const guestUser = createGuestUser();
      await this.setStorageValue(STORAGE_KEYS.USER, guestUser);
      
      this.userCache = guestUser;
      this.cacheTimestamp = now;
      return guestUser;
      
    } catch (error) {
      logError('Error getting user', error);
      const guestUser = createGuestUser();

      try {
        await this.setStorageValue(STORAGE_KEYS.USER, guestUser);
      } catch (saveError) {
        logError('Failed to save guest user', saveError);
      }
      
      this.userCache = guestUser;
      this.cacheTimestamp = now;
      return guestUser;
    }
  }

  /**
   * Update user profile with optimistic caching and validation
   */
  static async updateProfile(updates: Partial<Pick<User, 'displayName' | 'avatar'>>): Promise<User> {
    if (!updates || typeof updates !== 'object') {
      throw new Error('Updates are required and must be an object');
    }
    if (Object.keys(updates).length === 0) {
      throw new Error('At least one field must be updated');
    }

    try {
      const user = await this.getCurrentUser();
      const updatedUser = { ...user, ...updates };
      
      // Validate updated user
      if (!isValidUser(updatedUser)) {
        throw new Error('Invalid user data after update');
      }
      
      await this.setStorageValue(STORAGE_KEYS.USER, updatedUser);
      
      // Update cache
      this.userCache = updatedUser;
      this.cacheTimestamp = Date.now();
      
      return updatedUser;
    } catch (error) {
      logError('Error updating user profile', error);
      throw error;
    }
  }

  /**
   * Add points with level calculation and validation
   */
  static async addPoints(points: number): Promise<User> {
    // Enhanced validation
    if (typeof points !== 'number') {
      throw new Error('Points must be a number');
    }
    
    if (!Number.isInteger(points)) {
      throw new Error('Points must be an integer');
    }
    
    if (points < 0) {
      throw new Error('Points cannot be negative');
    }
    
    if (points > 10000) {
      throw new Error('Cannot add more than 10000 points at once');
    }

    try {
      const user = await this.getCurrentUser();
      
      // Validate current user state
      if (!user || !isValidUser(user)) {
        throw new Error('Invalid user state detected');
      }
      
      // Calculate new points and level with overflow protection
      const currentPoints = user.points || 0;
      const newPoints = Math.min(currentPoints + points, 1000000);
      
      // Ensure level calculation is safe
      const levelBase = Math.max(newPoints / 100, 0);
      const newLevel = Math.min(Math.floor(Math.sqrt(levelBase)) + 1, 100);
      
      const updatedUser = { 
        ...user, 
        points: newPoints, 
        level: newLevel,
        lastLoginAt: new Date().toISOString() // Update last activity
      };
      
      // Strict validation of updated user
      if (!isValidUser(updatedUser)) {
        throw new Error('User data validation failed after points update');
      }
      
      await this.setStorageValue(STORAGE_KEYS.USER, updatedUser);
      
      // Update cache
      this.userCache = updatedUser;
      this.cacheTimestamp = Date.now();
      
      return updatedUser;
    } catch (error) {
      logError('Error adding points', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to add points due to unexpected error');
    }
  }

  /**
   * Add badge to user (prevents duplicates) with validation
   */
  static async addBadge(badge: string): Promise<User> {
    validateBadge(badge);

    try {
      const user = await this.getCurrentUser();
      
      // Check if user already has this badge
      if (user.badges.includes(badge)) {
        return user;
      }
      
      const updatedUser = { ...user, badges: [...user.badges, badge] };
      await this.setStorageValue(STORAGE_KEYS.USER, updatedUser);
      
      // Update cache
      this.userCache = updatedUser;
      this.cacheTimestamp = Date.now();
      
      return updatedUser;
    } catch (error) {
      logError('Error adding badge', error);
      throw error;
    }
  }

  /**
   * Save user progress data with validation
   */
  static async saveUserProgress(key: string, data: unknown): Promise<void> {
    validateKey(key);
    if (data === undefined) {
      throw new Error('Progress data cannot be undefined');
    }

    try {
      await this.setStorageValue(key, data);
    } catch (error) {
      logError(`Error saving ${key}`, error);
      throw error;
    }
  }

  /**
   * Get user progress data with validation
   */
  static async getUserProgress<T = unknown>(key: string): Promise<T | null> {
    validateKey(key);

    try {
      return await this.getStorageValue<T>(key);
    } catch (error) {
      logError(`Error getting ${key}`, error);
      return null;
    }
  }

  /**
   * Clear all user data
   */
  static async clearAllUserData(): Promise<void> {
    try {
      const deletePromises = Object.values(STORAGE_KEYS).map(key => 
        this.deleteStorageValue(key)
      );
      
      await Promise.all(deletePromises);
      
      // Clear cache
      this.userCache = null;
      this.cacheTimestamp = 0;
      
    } catch (error) {
      logError('Error clearing user data', error);
      throw error;
    }
  }

  /**
   * Bulk operations for better performance with validation
   */
  static async bulkSave(data: Record<string, unknown>): Promise<void> {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object');
    }
    if (Object.keys(data).length === 0) {
      throw new Error('At least one key-value pair must be provided');
    }

    // Validate all keys
    Object.keys(data).forEach(key => validateKey(key));

    try {
      const savePromises = Object.entries(data).map(([key, value]) =>
        this.setStorageValue(key, value)
      );
      
      await Promise.all(savePromises);
    } catch (error) {
      logError('Error in bulk save', error);
      throw error;
    }
  }

  /**
   * Get storage usage statistics
   */
  static async getStorageStats(): Promise<{
    totalKeys: number;
    userDataExists: boolean;
    cacheStatus: 'valid' | 'expired' | 'empty';
  }> {
    try {
      const allKeys = await keys();
      const userDataExists = await this.getStorageValue(STORAGE_KEYS.USER) !== null;
      
      const now = Date.now();
      const cacheStatus = this.userCache 
        ? (now - this.cacheTimestamp) < this.CACHE_DURATION 
          ? 'valid' 
          : 'expired'
        : 'empty';
      
      return {
        totalKeys: allKeys.length,
        userDataExists,
        cacheStatus
      };
    } catch (error) {
      logError('Error getting storage stats', error);
      return {
        totalKeys: 0,
        userDataExists: false,
        cacheStatus: 'empty'
      };
    }
  }

  /**
   * Initialize cache cleanup
   */
  static initialize(): void {
    if (!this.cacheCleanupInterval) {
      // Clean cache every 10 minutes
      this.cacheCleanupInterval = setInterval(() => {
        const now = Date.now();
        if (this.userCache && (now - this.cacheTimestamp) > this.CACHE_DURATION) {
          this.clearCache();
        }
      }, 10 * 60 * 1000);
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  static clearCache(): void {
    this.userCache = null;
    this.cacheTimestamp = 0;
  }

  /**
   * Cleanup resources and intervals
   */
  static cleanup(): void {
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
      this.cacheCleanupInterval = null;
    }
    this.clearCache();
  }
}

export default LocalStorageService;
