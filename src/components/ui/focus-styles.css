/* Enhanced focus styles for better accessibility */

/* Global focus style */
:focus {
  outline: 2px solid rgba(20, 184, 166, 0.5);
  outline-offset: 2px;
}

/* Focus style for interactive elements */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid rgba(20, 184, 166, 0.7);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(20, 184, 166, 0.2);
}

/* Skip link focus style */
.skip-link:focus {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.5rem 1rem;
  background-color: white;
  z-index: 9999;
  text-decoration: none;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Focus style for form elements */
input:focus,
textarea:focus,
select:focus {
  border-color: rgba(20, 184, 166, 0.7);
}

/* Only show focus styles for keyboard navigation */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
  box-shadow: none;
}

.js-focus-visible .focus-visible {
  outline: 2px solid rgba(20, 184, 166, 0.7);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(20, 184, 166, 0.2);
}
